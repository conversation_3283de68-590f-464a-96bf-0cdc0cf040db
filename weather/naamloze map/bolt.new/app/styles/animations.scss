.animated {
  animation-fill-mode: both;
  animation-duration: var(--animate-duration, 0.2s);
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);

  &.fadeInRight {
    animation-name: fadeInRight;
  }

  &.fadeOutRight {
    animation-name: fadeOutRight;
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

.dropdown-animation {
  opacity: 0;
  animation: fadeMoveDown 0.15s forwards;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeMoveDown {
  to {
    opacity: 1;
    transform: translateY(6px);
  }
}
