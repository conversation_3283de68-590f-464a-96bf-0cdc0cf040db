name: "Bug report"
description: Create a report to help us improve
body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting an issue :pray:.

        This issue tracker is for bugs and issues found with [Bolt.new](https://bolt.new).
        If you experience issues related to WebContainer, please file an issue in our [WebContainer repo](https://github.com/stackblitz/webcontainer-core), or file an issue in our [StackBlitz core repo](https://github.com/stackblitz/core) for issues with StackBlitz.

        The more information you fill in, the better we can help you.
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: Provide a clear and concise description of what you're running into.
    validations:
      required: true
  - type: input
    id: link
    attributes:
      label: Link to the Bolt URL that caused the error
      description: Please do not delete it after reporting!
    validations:
      required: true
  - type: checkboxes
    id: checkboxes
    attributes:
      label: Validations
      description: Before submitting the issue, please make sure you do the following
      options:
        - label: "Please make your project public or accessible by URL. This will allow anyone trying to help you to easily reproduce the issue and provide assistance."
          required: true
  - type: markdown
    attributes:
      value: |
        ![Making your project public](https://github.com/stackblitz/bolt.new/blob/main/public/project-visibility.jpg?raw=true)
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce
      description: Describe the steps we have to take to reproduce the behavior.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: Provide a clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      label: Screen Recording / Screenshot
      description: If applicable, **please include a screen recording** (preferably) or screenshot showcasing the issue. This will assist us in resolving your issue <u>quickly</u>.
  - type: textarea
    id: platform
    attributes:
      label: Platform
      value: |
        - OS: [e.g. macOS, Windows, Linux]
        - Browser: [e.g. Chrome, Safari, Firefox]
        - Version: [e.g. 91.1]
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
